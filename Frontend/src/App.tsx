import { BrowserRouter, Routes, Route } from "react-router-dom";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { HomePage } from "@/pages/HomePage";
import { AnalysisPage } from "@/pages/AnalysisPage";
import { WaveformsPage } from "@/pages/WaveformsPage";
import { SettingsPage } from "@/pages/SettingsPage";
import { AccountPage } from "@/pages/AccountPage";

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<DashboardLayout />}>
          <Route index element={<HomePage />} />
          <Route path="analysis" element={<AnalysisPage />} />
          <Route path="waveforms" element={<WaveformsPage />} />
          <Route path="settings" element={<SettingsPage />} />
          <Route path="account" element={<AccountPage />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;
