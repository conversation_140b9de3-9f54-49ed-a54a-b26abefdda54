// Import and re-export HFOEvent from the canonical types file
import type { HFOEvent } from "@/types/hfo";
export type { HFOEvent };

export interface AnalysisMetadata {
  filename: string;
  sampling_rate: number;
  duration_seconds: number;
  channels: string[];
  processing_time: number;
}

export interface AnalysisStatistics {
  total_hfos: number;
  hfo_density: number;
  channels_with_hfos: string[];
  hfo_rate_per_channel?: Record<string, number>;
}

export interface AnalysisResults {
  metadata: AnalysisMetadata;
  statistics: AnalysisStatistics;
  channel_data: Record<string, number[]>;
  hfo_events: HFOEvent[];
  download_urls?: {
    results_json: string;
    hfo_events_csv: string;
  };
}
