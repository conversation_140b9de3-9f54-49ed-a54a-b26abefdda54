import React, { useMemo } from "react";
import { ChannelRow } from "./ChannelRow";
import { PlotlyChannelRow } from "./PlotlyChannelRow";
import type { HFOEvent, HFOType } from "@/types/hfo";

interface ChannelGridProps {
  channelData: Record<string, number[]>;
  visibleChannels: string[];
  timeWindow: [number, number];
  samplingRate: number;
  hfoEvents?: HFOEvent[];
  channelHeight?: number;
  showHFOMarkers?: boolean;
  usePlotly?: boolean;
  visibleHFOTypes?: HFOType[];
}

export const ChannelGrid: React.FC<ChannelGridProps> = ({
  channelData,
  visibleChannels,
  timeWindow,
  samplingRate,
  hfoEvents = [],
  channelHeight = 80,
  showHFOMarkers = true,
  usePlotly = false,
  visibleHFOTypes,
}) => {
  const filteredHfoEvents = useMemo(() => {
    if (!showHFOMarkers || !hfoEvents.length) return {};

    const eventsByChannel: Record<string, HFOEvent[]> = {};

    hfoEvents.forEach((event) => {
      // Filter by time window
      if (event.start_time >= timeWindow[0] && event.start_time <= timeWindow[1]) {
        // Filter by HFO type if specified
        const eventType = event.type || "accepted";
        if (visibleHFOTypes && !visibleHFOTypes.includes(eventType)) {
          return;
        }

        if (!eventsByChannel[event.channel]) {
          eventsByChannel[event.channel] = [];
        }
        eventsByChannel[event.channel].push(event);
      }
    });

    return eventsByChannel;
  }, [hfoEvents, timeWindow, showHFOMarkers, visibleHFOTypes]);

  if (visibleChannels.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center text-gray-500">
        <div className="text-center">
          <p className="text-sm">No channels selected</p>
          <p className="text-xs mt-1">Select channels from the panel to view their signals</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-y-auto bg-white">
      <div className="relative h-full flex flex-col">
        {/* Time axis header */}
        <div className="sticky top-0 z-10 bg-white border-b border-gray-200 h-8 flex items-center px-2">
          <div className="text-xs text-gray-600 flex justify-between w-full px-12">
            <span>{timeWindow[0].toFixed(1)}s</span>
            <span className="font-medium">Time Window</span>
            <span>{timeWindow[1].toFixed(1)}s</span>
          </div>
        </div>

        {/* Channel rows */}
        <div className="flex-1 overflow-y-auto">
          {visibleChannels.map((channel, index) => {
            const data = channelData[channel];
            if (!data || data.length === 0) return null;

            const RowComponent = usePlotly ? PlotlyChannelRow : ChannelRow;

            return (
              <RowComponent
                key={channel}
                channelName={channel}
                data={data}
                timeWindow={timeWindow}
                samplingRate={samplingRate}
                height={channelHeight}
                hfoEvents={filteredHfoEvents[channel] || []}
                showHFOMarkers={showHFOMarkers}
                isEven={index % 2 === 0}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};
