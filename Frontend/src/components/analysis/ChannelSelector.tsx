import React from 'react';
import { Layers, CheckSquare, Square } from 'lucide-react';

interface ChannelSelectorProps {
  channels: string[];
  selectedChannels: string[];
  onChannelToggle: (channel: string) => void;
  onSelectAll: () => void;
  onClearAll: () => void;
  hfoCountByChannel?: Record<string, number>;
}

export const ChannelSelector: React.FC<ChannelSelectorProps> = ({
  channels,
  selectedChannels,
  onChannelToggle,
  onSelectAll,
  onClearAll,
  hfoCountByChannel = {},
}) => {
  const totalHfos = Object.values(hfoCountByChannel).reduce((sum, count) => sum + count, 0);
  const channelsWithHfos = Object.keys(hfoCountByChannel).filter(ch => hfoCountByChannel[ch] > 0).length;

  return (
    <div className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <h3 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
          <Layers className="w-4 h-4" />
          Channel Selection
        </h3>
        <div className="mt-2 text-xs text-gray-600">
          {selectedChannels.length} of {channels.length} selected
        </div>
      </div>

      {/* Control Buttons */}
      <div className="p-3 bg-white border-b border-gray-200 flex gap-2">
        <button
          onClick={onSelectAll}
          className="flex-1 px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
        >
          Select All
        </button>
        <button
          onClick={onClearAll}
          className="flex-1 px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors"
        >
          Clear All
        </button>
      </div>

      {/* HFO Summary */}
      {totalHfos > 0 && (
        <div className="p-3 bg-blue-50 border-b border-blue-200">
          <div className="text-xs font-medium text-blue-900">HFO Summary</div>
          <div className="mt-1 text-xs text-blue-700">
            {totalHfos} HFOs in {channelsWithHfos} channel{channelsWithHfos !== 1 ? 's' : ''}
          </div>
        </div>
      )}

      {/* Channel List */}
      <div className="flex-1 overflow-y-auto p-3">
        <div className="space-y-1">
          {channels.map(channel => {
            const isSelected = selectedChannels.includes(channel);
            const hfoCount = hfoCountByChannel[channel] || 0;
            const hasHfos = hfoCount > 0;

            return (
              <label
                key={channel}
                className={`
                  flex items-center gap-2 p-2 rounded cursor-pointer transition-all
                  ${isSelected
                    ? 'bg-white border border-gray-300 shadow-sm'
                    : 'hover:bg-white hover:border-gray-200 border border-transparent'
                  }
                  ${hasHfos ? 'ring-1 ring-red-100' : ''}
                `}
              >
                <div className="flex items-center">
                  {isSelected ? (
                    <CheckSquare className="w-4 h-4 text-blue-600" />
                  ) : (
                    <Square className="w-4 h-4 text-gray-400" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <span className={`
                      text-sm font-medium truncate
                      ${isSelected ? 'text-gray-900' : 'text-gray-600'}
                    `}>
                      {channel}
                    </span>
                    {hasHfos && (
                      <span className="ml-2 px-1.5 py-0.5 text-xs font-medium text-red-700 bg-red-100 rounded">
                        {hfoCount}
                      </span>
                    )}
                  </div>
                  <div
                    className="h-0.5 mt-1 rounded-full transition-all"
                    style={{
                      background: isSelected
                        ? hasHfos
                          ? 'linear-gradient(90deg, #dc2626 0%, #000000 100%)'
                          : '#000000'
                        : '#e5e7eb',
                      opacity: isSelected ? 1 : 0.5
                    }}
                  />
                </div>
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => onChannelToggle(channel)}
                  className="sr-only"
                />
              </label>
            );
          })}
        </div>
      </div>

      {/* Footer Info */}
      <div className="p-3 border-t border-gray-200 bg-white">
        <div className="text-xs text-gray-500">
          <div className="flex items-center gap-1 mb-1">
            <div className="w-2 h-2 bg-red-500 rounded-full" />
            <span>Channels with HFO events</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full" />
            <span>No HFO events detected</span>
          </div>
        </div>
      </div>
    </div>
  );
};