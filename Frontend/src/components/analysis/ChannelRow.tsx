import React, { useRef, useEffect, useMemo } from 'react';
import type { HFOEvent } from '@/types/hfo';
import { HFO_TYPE_COLORS } from '@/types/hfo';

interface ChannelRowProps {
  channelName: string;
  data: number[];
  timeWindow: [number, number];
  samplingRate: number;
  height?: number;
  hfoEvents?: HFOEvent[];
  showHFOMarkers?: boolean;
  isEven?: boolean;
}

export const ChannelRow: React.FC<ChannelRowProps> = ({
  channelName,
  data,
  timeWindow,
  samplingRate,
  height = 80,
  hfoEvents = [],
  showHFOMarkers = true,
  isEven = true,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const windowedData = useMemo(() => {
    const startIdx = Math.floor(timeWindow[0] * samplingRate);
    const endIdx = Math.floor(timeWindow[1] * samplingRate);
    return data.slice(startIdx, endIdx);
  }, [data, timeWindow, samplingRate]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth * window.devicePixelRatio;
    canvas.height = height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background
    ctx.fillStyle = isEven ? '#fafafa' : '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    if (windowedData.length === 0) return;

    // Calculate scaling factors
    const xScale = (canvas.offsetWidth - 100) / windowedData.length;
    const dataMin = Math.min(...windowedData);
    const dataMax = Math.max(...windowedData);
    const dataRange = dataMax - dataMin || 1;
    const yScale = (height - 20) / dataRange;
    const yOffset = height / 2;

    // Draw zero line
    ctx.strokeStyle = '#e5e7eb';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(60, yOffset);
    ctx.lineTo(canvas.offsetWidth, yOffset);
    ctx.stroke();
    ctx.setLineDash([]);

    // Draw signal waveform
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();

    windowedData.forEach((value, index) => {
      const x = 60 + index * xScale;
      const y = yOffset - (value - (dataMax + dataMin) / 2) * yScale;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw HFO markers with color based on type
    if (showHFOMarkers && hfoEvents.length > 0) {
      // Group HFOs by type for better rendering
      const groupedEvents = hfoEvents.reduce((acc, event) => {
        const type = event.type || 'accepted';
        if (!acc[type]) acc[type] = [];
        acc[type].push(event);
        return acc;
      }, {} as Record<string, HFOEvent[]>);

      // Draw each type of HFO with its specific color
      Object.entries(groupedEvents).forEach(([type, events]) => {
        const color = HFO_TYPE_COLORS[type as keyof typeof HFO_TYPE_COLORS] || HFO_TYPE_COLORS.accepted;

        events.forEach(event => {
          const startX = 60 + ((event.start_time - timeWindow[0]) * samplingRate * xScale);
          const endX = 60 + ((event.end_time - timeWindow[0]) * samplingRate * xScale);
          const width = Math.max(endX - startX, 3);

          // Draw vertical line at HFO start
          ctx.strokeStyle = color;
          ctx.lineWidth = 1.5;
          ctx.globalAlpha = 0.8;

          // Use dotted line for rejected_long type
          if (event.type === 'rejected_long') {
            ctx.setLineDash([2, 2]);
          }

          ctx.beginPath();
          ctx.moveTo(startX, 5);
          ctx.lineTo(startX, height - 5);
          ctx.stroke();
          ctx.setLineDash([]);

          // Draw shaded region for HFO duration
          ctx.fillStyle = color;
          ctx.globalAlpha = 0.15;
          ctx.fillRect(startX, 5, width, height - 10);
          ctx.globalAlpha = 1;

          // Draw HFO indicator dot at center
          if (event.type === 'accepted') {
            ctx.beginPath();
            ctx.arc(startX + width / 2, yOffset, 3, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
          }
        });
      });
    }

    // Draw channel label
    ctx.fillStyle = '#111827';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText(channelName, 5, height / 2 + 4);

    // Draw scale indicator
    const scaleValue = Math.round(dataRange / 4);
    ctx.fillStyle = '#6b7280';
    ctx.font = '10px sans-serif';
    ctx.textAlign = 'right';
    ctx.fillText(`${scaleValue}μV`, 55, 15);

  }, [windowedData, height, channelName, hfoEvents, timeWindow, samplingRate, showHFOMarkers, isEven]);

  return (
    <div className="relative border-b border-gray-200 hover:bg-gray-50 transition-colors">
      <canvas
        ref={canvasRef}
        className="w-full"
        style={{ height: `${height}px` }}
      />
      {hfoEvents.length > 0 && (
        <div className="absolute top-1 right-2 text-xs font-medium flex gap-2">
          {(() => {
            const accepted = hfoEvents.filter(e => (e.type || 'accepted') === 'accepted').length;
            const rejected = hfoEvents.filter(e => e.type && e.type !== 'accepted').length;
            return (
              <>
                {accepted > 0 && (
                  <span style={{ color: HFO_TYPE_COLORS.accepted }}>
                    {accepted} ✓
                  </span>
                )}
                {rejected > 0 && (
                  <span style={{ color: HFO_TYPE_COLORS.rejected }}>
                    {rejected} ✗
                  </span>
                )}
              </>
            );
          })()}
        </div>
      )}
    </div>
  );
};