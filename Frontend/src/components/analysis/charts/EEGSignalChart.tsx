import React from "react";
import Plot from "react-plotly.js";
import type { HFOEvent } from "@/types/hfo";
import { HFO_TYPE_COLORS } from "@/types/hfo";

interface EEGSignalChartProps {
  selectedChannel: string;
  channelData: number[];
  hfoEvents: HFOEvent[];
  samplingRate: number;
  timeWindow: [number, number];
  duration: number;
  onTimeWindowChange: (window: [number, number]) => void;
}

export const EEGSignalChart: React.FC<EEGSignalChartProps> = ({
  selectedChannel,
  channelData,
  hfoEvents,
  samplingRate,
  timeWindow,
  duration,
  onTimeWindowChange,
}) => {
  // Filter HFOs for selected channel
  const channelHFOs = hfoEvents.filter((hfo) => hfo.channel === selectedChannel);

  // Group HFOs by type
  const hfosByType = channelHFOs.reduce((acc, hfo) => {
    const type = hfo.type || "accepted";
    if (!acc[type]) acc[type] = [];
    acc[type].push(hfo);
    return acc;
  }, {} as Record<string, typeof channelHFOs>);

  const plotData = [
    {
      type: "scatter" as const,
      mode: "lines" as const,
      name: selectedChannel,
      x: Array.from({ length: channelData.length }, (_, i) => i / samplingRate),
      y: channelData,
      line: { color: "#1e40af", width: 1 },
    },
    // Create separate traces for each HFO type
    ...Object.entries(hfosByType).map(([type, hfos]) => {
      const color = HFO_TYPE_COLORS[type as keyof typeof HFO_TYPE_COLORS] || HFO_TYPE_COLORS.accepted;

      return {
        type: "scatter" as const,
        mode: "markers" as const,
        name: `${type.charAt(0).toUpperCase() + type.slice(1)} HFOs`,
        x: hfos.map((hfo) => hfo.start_time),
        y: hfos.map((hfo) => {
          const idx = Math.floor(hfo.start_time * samplingRate);
          return channelData?.[idx] || 0;
        }),
        marker: {
          color: color,
          size: 10,
          symbol: type === "accepted" ? "triangle-up" : "circle",
        },
        text: hfos.map((hfo) => `Type: ${type}\nFreq: ${hfo.peak_frequency}Hz\nAmp: ${hfo.amplitude}`),
        hovertemplate: "%{text}<extra></extra>",
      };
    }),
  ];

  return (
    <div className="border rounded-lg p-4">
      <h3 className="text-lg font-semibold mb-3">EEG Signal with HFO Markers - {selectedChannel}</h3>
      <Plot
        data={plotData}
        layout={{
          height: 300,
          xaxis: {
            title: { text: "Time (seconds)" },
            range: timeWindow,
          },
          yaxis: {
            title: { text: "Amplitude (μV)" },
          },
          showlegend: true,
          hovermode: "closest",
        }}
        config={{ responsive: true }}
        style={{ width: "100%" }}
      />

      {/* Time Window Controls */}
      <div className="mt-4 flex items-center space-x-4">
        <button
          onClick={() => onTimeWindowChange([Math.max(0, timeWindow[0] - 10), timeWindow[1] - 10])}
          disabled={timeWindow[0] <= 0}
          className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
        >
          ← Previous
        </button>

        <span className="text-sm text-gray-600">
          {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
        </span>

        <button
          onClick={() => onTimeWindowChange([timeWindow[0] + 10, timeWindow[1] + 10])}
          disabled={timeWindow[1] >= duration}
          className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
        >
          Next →
        </button>

        <input
          type="range"
          min="1"
          max="60"
          value={timeWindow[1] - timeWindow[0]}
          onChange={(e) => {
            const windowSize = parseInt(e.target.value);
            onTimeWindowChange([timeWindow[0], timeWindow[0] + windowSize]);
          }}
          className="ml-4"
        />
        <span className="text-sm text-gray-600">Window: {timeWindow[1] - timeWindow[0]}s</span>
      </div>
    </div>
  );
};
