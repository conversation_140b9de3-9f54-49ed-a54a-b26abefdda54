import React, { useEffect, useState, useCallback, useRef, useMemo } from "react";
import { Download } from "lucide-react";
import { apiClient } from "@/services/api";
import type { HFOEvent, HFOType } from "@/types/hfo";
import { HFO_TYPE_LABELS, HFO_TYPE_COLORS } from "@/types/hfo";
import { ChannelGrid } from "./ChannelGrid";
import { ChannelSelector } from "./ChannelSelector";
import { TimeNavigationControls } from "./TimeNavigationControls";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";

interface AnalysisResults {
  metadata: {
    filename: string;
    sampling_rate: number;
    duration_seconds: number;
    channels: string[];
    processing_time: number;
    montage?: string;
    low_cutoff?: number;
    high_cutoff?: number;
  };
  statistics: {
    total_hfos: number;
    hfo_density: number;
    channels_with_hfos: string[];
    hfo_rate_per_channel?: Record<string, number>;
  };
  channel_data: Record<string, number[]>;
  hfo_events: HFOEvent[];
  download_urls?: {
    results_json: string;
    hfo_events_csv: string;
  };
}

interface ResultsViewerProps {
  jobId: string;
  onClose?: () => void;
}

export const ResultsViewer: React.FC<ResultsViewerProps> = ({ jobId, onClose }) => {
  const [results, setResults] = useState<AnalysisResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [timeWindow, setTimeWindow] = useState<[number, number]>([0, 10]);
  const [timeWindowSize, setTimeWindowSize] = useState(10);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [visibleHFOTypes, setVisibleHFOTypes] = useState<HFOType[]>(["accepted", "rejected", "rejected_long", "lfo_rejected", "noise_rejected"]);
  const [usePlotlyRows, setUsePlotlyRows] = useState(false);
  const channelViewRef = useRef<HTMLDivElement>(null);

  const fetchResults = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching results for job:", jobId);
      const response = await apiClient.get<AnalysisResults>(`/analysis/results/${jobId}`);
      console.log("Results response:", response.data);

      if (!response.data || !response.data.metadata) {
        console.error("Invalid response format:", response);
        throw new Error("Invalid results format");
      }

      // Log successful data
      console.log("Setting results with channels:", response.data.metadata.channels?.length);
      console.log("HFO events count:", response.data.hfo_events?.length);

      setResults(response.data);

      if (response.data.metadata.channels && response.data.metadata.channels.length > 0) {
        setSelectedChannels(response.data.metadata.channels);
      }
      console.log("Loading set to false");
      setLoading(false);
    } catch (err) {
      console.error("Error fetching results:", err);
      const error = err as { response?: { data?: { detail?: string } } };
      setError(error.response?.data?.detail || "Failed to load results");
      setLoading(false);
    }
  }, [jobId]);

  useEffect(() => {
    fetchResults();
  }, [fetchResults]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!results || e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key.toLowerCase()) {
        case "a": // Zoom in
          setTimeWindowSize((prev) => {
            const newSize = Math.max(1, prev / 2);
            const center = (timeWindow[0] + timeWindow[1]) / 2;
            const newStart = Math.max(0, Math.min(results.metadata.duration_seconds - newSize, center - newSize / 2));
            setTimeWindow([newStart, newStart + newSize]);
            return newSize;
          });
          break;
        case "d": // Zoom out
          setTimeWindowSize((prev) => {
            const newSize = Math.min(60, prev * 2);
            const center = (timeWindow[0] + timeWindow[1]) / 2;
            const newStart = Math.max(0, Math.min(results.metadata.duration_seconds - newSize, center - newSize / 2));
            setTimeWindow([newStart, newStart + newSize]);
            return newSize;
          });
          break;
        case "o": // Previous
          if (timeWindow[0] > 0) {
            const step = timeWindowSize / 2;
            const newStart = Math.max(0, timeWindow[0] - step);
            setTimeWindow([newStart, newStart + timeWindowSize]);
          }
          break;
        case "p": // Next
          if (timeWindow[1] < results.metadata.duration_seconds) {
            const step = timeWindowSize / 2;
            const newStart = Math.min(results.metadata.duration_seconds - timeWindowSize, timeWindow[0] + step);
            setTimeWindow([newStart, newStart + timeWindowSize]);
          }
          break;
        case "f": // Fullscreen
          if (channelViewRef.current) {
            if (!document.fullscreenElement) {
              channelViewRef.current.requestFullscreen();
            } else {
              document.exitFullscreen();
            }
          }
          break;
      }
    };

    document.addEventListener("keydown", handleKeyPress);
    return () => document.removeEventListener("keydown", handleKeyPress);
  }, [results, timeWindow, timeWindowSize]);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  // Calculate HFO statistics by type
  const hfoStatsByType = useMemo(() => {
    if (!results) return null;

    const stats = {
      total: results.hfo_events.length,
      accepted: results.hfo_events.filter((e) => e.type === "accepted").length,
      rejected: results.hfo_events.filter((e) => e.type === "rejected").length,
      rejected_long: results.hfo_events.filter((e) => e.type === "rejected_long").length,
      lfo_rejected: results.hfo_events.filter((e) => e.type === "lfo_rejected").length,
      noise_rejected: results.hfo_events.filter((e) => e.type === "noise_rejected").length,
    };
    return stats;
  }, [results]);

  // Filter HFO events by visible types
  const filteredHfoEvents = useMemo(() => {
    if (!results) return [];
    return results.hfo_events.filter((event) => {
      const eventType = event.type || "accepted";
      return visibleHFOTypes.includes(eventType);
    });
  }, [results, visibleHFOTypes]);

  const downloadResults = async (format: string) => {
    try {
      // For comprehensive report
      if (format === "report") {
        const response = await apiClient.get<{ download_url: string }>(`/analysis/download/${jobId}?format=report`);
        if (response.data && response.data.download_url) {
          window.open(response.data.download_url, "_blank");
        }
        return;
      }

      const response = await apiClient.get<{ download_url: string }>(`/analysis/download/${jobId}?format=${format}`);

      if (response.data && response.data.download_url) {
        window.open(response.data.download_url, "_blank");
      } else {
        handleLocalDownload(format);
      }
    } catch {
      handleLocalDownload(format);
    }
  };

  const handleLocalDownload = (format: string) => {
    if (!results) return;

    const data = format === "json" ? JSON.stringify(results, null, 2) : convertToCSV(results.hfo_events);
    const blob = new Blob([data], {
      type: format === "json" ? "application/json" : "text/csv",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `analysis_results_${jobId}.${format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const convertToCSV = (events: HFOEvent[]) => {
    const headers = ["Channel", "Start Time", "End Time", "Peak Frequency", "Amplitude"];
    const rows = events.map((e) => [e.channel, e.start_time, e.end_time, e.peak_frequency, e.amplitude]);
    return [headers, ...rows].map((row) => row.join(",")).join("\n");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading analysis results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <p className="text-red-800">{error}</p>
      </div>
    );
  }

  if (!results) return null;

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center border-b pb-4">
        <div>
          <h2 className="text-2xl font-semibold">HFO Analysis Results</h2>
          <p className="text-gray-600">{results.metadata.filename}</p>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => downloadResults("csv")}
            className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded text-sm flex items-center gap-1"
            title="Download HFO events"
          >
            <Download className="w-4 h-4" />
            Export
          </button>
          {onClose && (
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700 text-2xl">
              ×
            </button>
          )}
        </div>
      </div>

      {/* Main Graph View */}
      <div className="flex flex-col space-y-4" style={{ minHeight: "500px", maxHeight: "calc(100vh - 200px)" }}>
        {/* HFO Type Filters */}
        <Card className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-sm">HFO Event Filters</h3>
            <div className="flex items-center gap-2 text-xs">
              <button onClick={() => setUsePlotlyRows(!usePlotlyRows)} className="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700">
                {usePlotlyRows ? "Canvas Mode" : "Plotly Mode"}
              </button>
              {hfoStatsByType && (
                <span className="text-gray-500">
                  Showing {filteredHfoEvents.length} of {hfoStatsByType.total} events
                </span>
              )}
            </div>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            {(["accepted", "rejected", "rejected_long", "lfo_rejected", "noise_rejected"] as HFOType[]).map((type) => {
              const count = hfoStatsByType ? hfoStatsByType[type as keyof typeof hfoStatsByType] : 0;
              const isVisible = visibleHFOTypes.includes(type);
              return (
                <label key={type} className="flex items-center space-x-2 cursor-pointer">
                  <Checkbox
                    checked={isVisible}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setVisibleHFOTypes([...visibleHFOTypes, type]);
                      } else {
                        setVisibleHFOTypes(visibleHFOTypes.filter((t) => t !== type));
                      }
                    }}
                  />
                  <div className="flex items-center gap-1">
                    <span className="w-3 h-3 rounded" style={{ backgroundColor: HFO_TYPE_COLORS[type] }} />
                    <span className="text-xs text-gray-700">
                      {HFO_TYPE_LABELS[type]} ({count})
                    </span>
                  </div>
                </label>
              );
            })}
          </div>
        </Card>

        {/* Time Navigation Controls */}
        <TimeNavigationControls
          timeWindow={timeWindow}
          timeWindowSize={timeWindowSize}
          totalDuration={results.metadata.duration_seconds}
          onTimeWindowChange={setTimeWindow}
          onTimeWindowSizeChange={setTimeWindowSize}
          onReset={() => {
            setTimeWindow([0, 10]);
            setTimeWindowSize(10);
            setSelectedChannels(results.metadata.channels);
          }}
          onFullscreen={() => {
            if (channelViewRef.current) {
              if (!document.fullscreenElement) {
                channelViewRef.current.requestFullscreen();
              } else {
                document.exitFullscreen();
              }
            }
          }}
        />

        {/* Multi-Channel View */}
        <div ref={channelViewRef} className="flex border border-gray-200 rounded-lg overflow-hidden flex-1" style={{ minHeight: "400px" }}>
          {/* Channel Selector Sidebar */}
          {!isFullscreen && (
            <ChannelSelector
              channels={results.metadata.channels}
              selectedChannels={selectedChannels}
              onChannelToggle={(channel) => {
                setSelectedChannels((prev) => (prev.includes(channel) ? prev.filter((ch) => ch !== channel) : [...prev, channel]));
              }}
              onSelectAll={() => setSelectedChannels(results.metadata.channels)}
              onClearAll={() => setSelectedChannels([])}
              hfoCountByChannel={results.metadata.channels.reduce((acc, channel) => {
                acc[channel] = results.hfo_events.filter((h) => h.channel === channel).length;
                return acc;
              }, {} as Record<string, number>)}
            />
          )}

          {/* Channel Grid Display */}
          <div className="flex-1 bg-white overflow-hidden">
            <ChannelGrid
              channelData={results.channel_data}
              visibleChannels={selectedChannels}
              timeWindow={timeWindow}
              samplingRate={results.metadata.sampling_rate}
              hfoEvents={filteredHfoEvents}
              showHFOMarkers={true}
              usePlotly={usePlotlyRows}
              visibleHFOTypes={visibleHFOTypes}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
