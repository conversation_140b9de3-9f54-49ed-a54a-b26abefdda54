"""
User service for managing user preferences and notifications.
"""

import os
import re
from datetime import datetime
from typing import Any

import boto3
from botocore.exceptions import ClientError

from ..logging_config import get_logger

logger = get_logger(__name__)

# AWS resources
dynamodb = boto3.resource("dynamodb")
ses = boto3.client("ses", region_name="us-east-1")

# Environment configuration
PREFERENCES_TABLE_NAME = os.getenv("PREFERENCES_TABLE_NAME", "biormika-user-preferences")
JOBS_TABLE_NAME = os.getenv("JOBS_TABLE_NAME", "biormika-analysis-jobs")
SES_SENDER_EMAIL = os.getenv("SES_SENDER_EMAIL", "<EMAIL>")
EMAIL_REGEX = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

# Table references
preferences_table = dynamodb.Table(PREFERENCES_TABLE_NAME) if PREFERENCES_TABLE_NAME else None
jobs_table = dynamodb.Table(JOBS_TABLE_NAME) if JOBS_TABLE_NAME else None


class UserService:
    """Service for user preference and notification management."""

    @staticmethod
    def validate_email_format(email: str) -> bool:
        """Validate email format."""
        return bool(re.match(EMAIL_REGEX, email))

    @classmethod
    def get_user_preferences(cls, user_id: str) -> dict[str, Any]:
        """Get user preferences from DynamoDB."""
        if not preferences_table:
            return {
                "email": "<EMAIL>",
                "notification_enabled": True,
                "last_updated": datetime.utcnow().isoformat(),
            }

        response = preferences_table.get_item(Key={"user_id": user_id})

        if "Item" not in response:
            return {
                "email": "",
                "notification_enabled": True,
                "last_updated": datetime.utcnow().isoformat(),
            }

        item = response["Item"]
        return {
            "email": item.get("email", ""),
            "notification_enabled": item.get("notification_enabled", True),
            "last_updated": item.get("updated_at", item.get("created_at", "")),
        }

    @classmethod
    def update_user_preferences(
        cls,
        user_id: str,
        email: str,
        notification_enabled: bool
    ) -> dict[str, Any]:
        """Update user preferences in DynamoDB."""
        timestamp = datetime.utcnow().isoformat()

        if not preferences_table:
            return {
                "email": email,
                "notification_enabled": notification_enabled,
                "last_updated": timestamp,
            }

        # Check if user exists
        response = preferences_table.get_item(Key={"user_id": user_id})

        if "Item" not in response:
            # Create new user
            preferences_table.put_item(
                Item={
                    "user_id": user_id,
                    "email": email,
                    "notification_enabled": notification_enabled,
                    "created_at": timestamp,
                    "updated_at": timestamp,
                }
            )
        else:
            # Update existing user
            preferences_table.update_item(
                Key={"user_id": user_id},
                UpdateExpression="SET email = :email, notification_enabled = :enabled, updated_at = :updated",
                ExpressionAttributeValues={
                    ":email": email,
                    ":enabled": notification_enabled,
                    ":updated": timestamp,
                }
            )

        logger.info(f"Updated preferences for user {user_id}")

        return {
            "email": email,
            "notification_enabled": notification_enabled,
            "last_updated": timestamp,
        }

    @classmethod
    def send_verification_email(cls, email: str) -> dict[str, str]:
        """Send verification email using SES."""
        try:
            ses.send_email(
                Source=SES_SENDER_EMAIL,
                Destination={"ToAddresses": [email]},
                Message={
                    "Subject": {"Data": "Biormika - Email Verification"},
                    "Body": {
                        "Text": {
                            "Data": "Your email has been verified for Biormika HFO Analysis notifications.\n\n"
                                    "You will receive notifications when your analysis jobs complete."
                        }
                    }
                }
            )

            logger.info(f"Verification email sent to {email}")

            return {
                "status": "success",
                "message": "Verification email sent. Please check your inbox.",
            }

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error'].get('Message', '')

            if error_code == 'MessageRejected':
                if 'not verified' in error_message.lower():
                    logger.warning(f"SES sandbox mode: {error_message}")
                    raise ValueError(
                        "Email address not verified in SES sandbox. "
                        "Please contact support or verify your email address."
                    ) from e
                else:
                    raise ValueError(f"Email rejected: {error_message}") from e
            elif error_code == 'ConfigurationSet':
                raise ValueError(
                    "Email service configuration error. Please contact support."
                ) from e
            else:
                logger.error(f"SES error: {error_code} - {error_message}")
                raise ValueError(f"Failed to send email: {error_code}") from e

    @staticmethod
    def get_user_statistics(user_id: str) -> dict[str, int]:
        """Get user analysis statistics."""
        try:
            # For now, return empty stats matching frontend expectations
            # In production, would query jobs_table with user GSI
            return {
                "total_analyses": 0,
                "hfos_detected": 0,
            }

        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            return {
                "total_analyses": 0,
                "hfos_detected": 0,
            }

