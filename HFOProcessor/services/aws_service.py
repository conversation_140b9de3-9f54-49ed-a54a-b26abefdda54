"""
AWS Service Layer
Handles all AWS operations for HFO processing
"""

import os
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import boto3
from botocore.exceptions import ClientError

logger = logging.getLogger(__name__)


class AWSService:
    """Centralized AWS service for all AWS operations"""

    def __init__(self):
        """Initialize AWS clients and configurations"""
        # AWS clients
        self.s3_client = boto3.client("s3")
        self.sqs_client = boto3.client("sqs")
        self.dynamodb = boto3.resource("dynamodb")
        self.ses_client = boto3.client("ses")

        # Environment configurations
        self.sqs_queue_url = os.getenv("SQS_QUEUE_URL")
        self.s3_bucket_name = os.getenv("S3_BUCKET_NAME")
        self.jobs_table_name = os.getenv("JOBS_TABLE_NAME")
        self.preferences_table_name = os.getenv("PREFERENCES_TABLE_NAME")

        # DynamoDB tables
        self.jobs_table = self.dynamodb.Table(self.jobs_table_name)
        self.preferences_table = self.dynamodb.Table(self.preferences_table_name)

    # S3 Operations
    def download_file_from_s3(self, file_key: str, local_path: str) -> str:
        """
        Download a file from S3 to local storage

        Args:
            file_key: S3 key of the file
            local_path: Local path to save the file

        Returns:
            Path to the downloaded file
        """
        try:
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            logger.info(f"Downloading {file_key} from S3 to {local_path}")
            self.s3_client.download_file(self.s3_bucket_name, file_key, local_path)
            return local_path
        except ClientError as e:
            logger.error(f"Failed to download file from S3: {e}")
            raise

    def upload_file_to_s3(self, local_path: str, s3_key: str,
                          content_type: str = "application/octet-stream") -> str:
        """
        Upload a file to S3

        Args:
            local_path: Local file path
            s3_key: S3 key for the file
            content_type: MIME type of the file

        Returns:
            S3 key of the uploaded file
        """
        try:
            logger.info(f"Uploading {local_path} to s3://{self.s3_bucket_name}/{s3_key}")
            self.s3_client.upload_file(
                local_path,
                self.s3_bucket_name,
                s3_key,
                ExtraArgs={"ContentType": content_type}
            )
            return s3_key
        except ClientError as e:
            logger.error(f"Failed to upload file to S3: {e}")
            raise

    def put_object_to_s3(self, content: Any, s3_key: str,
                         content_type: str = "application/octet-stream") -> str:
        """
        Put object content directly to S3

        Args:
            content: Content to upload
            s3_key: S3 key for the object
            content_type: MIME type of the content

        Returns:
            S3 key of the uploaded object
        """
        try:
            logger.info(f"Putting object to s3://{self.s3_bucket_name}/{s3_key}")
            self.s3_client.put_object(
                Bucket=self.s3_bucket_name,
                Key=s3_key,
                Body=content,
                ContentType=content_type
            )
            return s3_key
        except ClientError as e:
            logger.error(f"Failed to put object to S3: {e}")
            raise

    def generate_presigned_url(self, s3_key: str, expiration: int = 86400) -> str:
        """
        Generate a presigned URL for S3 object

        Args:
            s3_key: S3 key of the object
            expiration: URL expiration time in seconds

        Returns:
            Presigned URL
        """
        try:
            url = self.s3_client.generate_presigned_url(
                "get_object",
                Params={"Bucket": self.s3_bucket_name, "Key": s3_key},
                ExpiresIn=expiration
            )
            return url
        except ClientError as e:
            logger.error(f"Failed to generate presigned URL: {e}")
            raise

    # SQS Operations
    def poll_sqs_queue(self, max_messages: int = 1,
                       visibility_timeout: int = 3600,
                       wait_time: int = 20) -> List[Dict]:
        """
        Poll SQS queue for messages

        Args:
            max_messages: Maximum number of messages to retrieve
            visibility_timeout: Message visibility timeout in seconds
            wait_time: Long polling wait time in seconds

        Returns:
            List of messages
        """
        try:
            response = self.sqs_client.receive_message(
                QueueUrl=self.sqs_queue_url,
                MaxNumberOfMessages=max_messages,
                VisibilityTimeout=visibility_timeout,
                WaitTimeSeconds=wait_time,
            )
            return response.get("Messages", [])
        except ClientError as e:
            logger.error(f"Error polling SQS queue: {e}")
            return []

    def delete_sqs_message(self, receipt_handle: str) -> bool:
        """
        Delete a message from SQS queue

        Args:
            receipt_handle: Message receipt handle

        Returns:
            True if successful
        """
        try:
            self.sqs_client.delete_message(
                QueueUrl=self.sqs_queue_url,
                ReceiptHandle=receipt_handle,
            )
            return True
        except ClientError as e:
            logger.error(f"Failed to delete SQS message: {e}")
            return False

    # DynamoDB Operations
    def update_job_status(self, job_id: str, status: str,
                         attributes: Optional[Dict] = None) -> bool:
        """
        Update job status in DynamoDB

        Args:
            job_id: Job identifier
            status: New status
            attributes: Additional attributes to update

        Returns:
            True if successful
        """
        try:
            # Base update expression
            update_expr = "SET #status = :status, updated_at = :updated_at"
            expr_values = {
                ":status": status,
                ":updated_at": datetime.utcnow().isoformat(),
            }
            expr_names = {"#status": "status"}

            # Add additional attributes
            if attributes:
                for key, value in attributes.items():
                    safe_key = key.replace(".", "_")
                    update_expr += f", {safe_key} = :{safe_key}"
                    expr_values[f":{safe_key}"] = value

            self.jobs_table.update_item(
                Key={"job_id": job_id},
                UpdateExpression=update_expr,
                ExpressionAttributeNames=expr_names,
                ExpressionAttributeValues=expr_values,
            )
            return True
        except ClientError as e:
            logger.error(f"Failed to update job status: {e}")
            return False

    def get_job_details(self, job_id: str) -> Optional[Dict]:
        """
        Get job details from DynamoDB

        Args:
            job_id: Job identifier

        Returns:
            Job details dictionary or None
        """
        try:
            response = self.jobs_table.get_item(Key={"job_id": job_id})
            return response.get("Item")
        except ClientError as e:
            logger.error(f"Failed to get job details: {e}")
            return None

    def get_user_preferences(self, user_id: str) -> Optional[Dict]:
        """
        Get user preferences from DynamoDB

        Args:
            user_id: User identifier

        Returns:
            User preferences dictionary or None
        """
        try:
            response = self.preferences_table.get_item(Key={"user_id": user_id})
            return response.get("Item")
        except ClientError as e:
            logger.error(f"Failed to get user preferences: {e}")
            return None

    # SES Operations
    def send_templated_email(self, to_addresses: List[str], template: str,
                            template_data: Dict, sender_email: str) -> bool:
        """
        Send templated email via SES

        Args:
            to_addresses: List of recipient email addresses
            template: Email template name
            template_data: Template data dictionary
            sender_email: Sender email address

        Returns:
            True if successful
        """
        try:
            import json
            self.ses_client.send_templated_email(
                Source=sender_email,
                Destination={"ToAddresses": to_addresses},
                Template=template,
                TemplateData=json.dumps(template_data),
            )
            logger.info(f"Email sent to {to_addresses} using template {template}")
            return True
        except ClientError as e:
            logger.error(f"Failed to send email: {e}")
            return False

    def validate_environment(self) -> Tuple[bool, List[str]]:
        """
        Validate required environment variables

        Returns:
            Tuple of (is_valid, missing_variables)
        """
        required_vars = [
            "SQS_QUEUE_URL",
            "S3_BUCKET_NAME",
            "JOBS_TABLE_NAME",
            "PREFERENCES_TABLE_NAME",
        ]

        missing = []
        for var in required_vars:
            if not os.getenv(var):
                missing.append(var)

        return len(missing) == 0, missing